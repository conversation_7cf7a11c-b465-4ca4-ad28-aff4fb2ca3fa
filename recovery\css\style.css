:root{
  --bg-dark: #4a4a4a;
  --bg-darker: #3d3d3d;
  --bg-darkest: #2f2f2f;
  --cement-light: #8a8a8a;
  --cement-medium: #707070;
  --cement-dark: #5a5a5a;
  --card-bg: rgba(106, 106, 106, 0.85);
  --card-bg-hover: rgba(120, 120, 120, 0.9);
  --card-border: rgba(0, 0, 0, 0.2);
  --text-primary: #E8E8E8;
  --text-secondary: #C5C5C5;
  --shadow-soft: rgba(0, 0, 0, 0.15);
  --shadow-medium: rgba(0, 0, 0, 0.25);
}

/* Progress Bar Styles */
#demoProgress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
}

.progress-bar {
  height: 100%;
  width: 100%;
  background: transparent;
  border-radius: 0 3px 3px 0;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, var(--cement-medium), var(--cement-light));
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  right: 20px;
  color: var(--cement-medium);
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

/* Reset and base */
body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  background: linear-gradient(135deg, var(--bg-darkest) 0%, var(--bg-dark) 50%, var(--bg-darker) 100%);
  font-family: 'Segoe UI', sans-serif;
  color: var(--text-primary);
}

#intro {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, var(--bg-darkest) 0%, var(--bg-dark) 50%, var(--bg-darker) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 15px;
  box-sizing: border-box;
  transition: opacity 1s ease-out;
}

.logo-container {
  text-align: center;
  width: 100%;
  max-width: 800px;
  z-index: 10;
  animation: fadeIn 2s ease forwards;
}

.logo {
  width: 100%;
  max-width: min(400px, 60vw);
  height: auto;
  animation: pulse 3s infinite;
  filter: drop-shadow(0 0 20px rgba(138, 138, 138, 0.4));
  margin-bottom: 2rem;
}

#intro h1 {
  color: var(--text-secondary);
  font-size: clamp(1.2rem, 4vw, 1.6rem);
  margin-top: 1.5rem;
  opacity: 0;
  animation: fadeInText 1.5s ease forwards;
  animation-delay: 0.8s;
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInText {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    filter: drop-shadow(0 0 10px rgba(138, 138, 138, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 25px rgba(138, 138, 138, 0.6));
  } 
  100% {
    filter: drop-shadow(0 0 10px rgba(138, 138, 138, 0.3));
  }
}

/* Main Section Styles */
body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, var(--bg-darkest) 0%, var(--bg-dark) 50%, var(--bg-darker) 100%);
  color: var(--text-primary);
  padding-bottom: 200px;
  min-height: 150vh;
}

.floating-particle {
  will-change: transform;
  pointer-events: none;
}

* {
  transition: all .25s cubic-bezier(.4,0,.2,1);
}

.container {
  padding: 20px;
  max-width: 1200px;
  margin: auto;
  min-height: 100vh;
}

/* Water Gallery Slideshow */
#water-gallery {
  position: relative;
  width: 100%;
  margin: 0 auto;
}

.slideshow-container {
  position: relative;
  margin: 20px auto;
  width: 95%;
  max-width: 1000px;
  height: 60vh;
  min-height: 300px;
  max-height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  box-shadow: 0 6px 20px var(--shadow-soft);
  background: linear-gradient(145deg, var(--card-bg), rgba(90, 90, 90, 0.8));
  border: 1px solid var(--card-border);
}

.slide {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1.5s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slide.fade {
  opacity: 1;
}

.slide img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  object-position: center;
  filter: drop-shadow(0 0 15px rgba(138, 138, 138, 0.3));
}

/* Category Grid - 2 columns on mobile */
#categoryList {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  max-width: 1200px;
  margin: 30px auto 0;
  padding: 15px;
}

.category-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  text-align: center;
  font-weight: bold;
  border-radius: 18px;
  cursor: pointer;
  box-shadow: 0 4px 12px var(--shadow-soft);
  color: var(--text-primary);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  animation: cardSlideIn 0.6s ease forwards;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.category-card:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(138, 138, 138, 0.1), transparent);
  transform: translateX(-100%);
}

.category-card:hover:before {
  animation: shine 1.5s;
}

@keyframes shine {
  100% {
    transform: translateX(100%);
  }
}

.category-card:hover {
  transform: scale(1.03) rotate(0.5deg);
  background: var(--card-bg-hover);
  box-shadow: 0 6px 18px var(--shadow-medium);
  border-color: rgba(0, 0, 0, 0.3);
}

.category-card:active {
  transform: scale(0.98) rotate(-0.5deg);
  transition: all 0.1s ease;
}

.category-card img {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
  object-fit: contain;
  transition: transform 0.3s ease;
  filter: drop-shadow(0 0 8px rgba(138, 138, 138, 0.4));
}

.category-card h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

/* Category specific colors - subtle cement variations */
.cat-hot { border-color: rgba(0, 0, 0, 0.25); }
.cat-ice { border-color: rgba(0, 0, 0, 0.25); }
.cat-frappe { border-color: rgba(0, 0, 0, 0.25); }
.cat-refreshing { border-color: rgba(0, 0, 0, 0.25); }
.cat-juices { border-color: rgba(0, 0, 0, 0.25); }
.cat-milkshakes { border-color: rgba(0, 0, 0, 0.25); }
.cat-smoothies { border-color: rgba(0, 0, 0, 0.25); }
.cat-donuts { border-color: rgba(0, 0, 0, 0.25); }
.cat-cake { border-color: rgba(0, 0, 0, 0.25); }

/* Top Bar & Navigation */
#topBar {
  display: none;
  margin: 20px auto 10px;
  text-align: center;
  max-width: 1200px;
  padding: 0 20px;
}

#categoryBar {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

#categoryBar button {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  padding: 12px 20px;
  border-radius: 20px;
  color: var(--text-secondary);
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  font-size: 14px;
  min-width: 80px;
  box-shadow: 0 2px 8px var(--shadow-soft);
}

#categoryBar button:hover,
#categoryBar button.active {
  background: var(--cement-medium);
  color: var(--text-primary);
  transform: translateY(-1px);
  border-color: rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

#backBtn {
  background: var(--cement-medium);
  color: var(--text-primary);
  border: 1px solid var(--card-border);
  padding: 12px 28px;
  border-radius: 25px;
  cursor: pointer;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 4px 12px var(--shadow-soft);
}

#backBtn:hover {
  background: var(--cement-light);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px var(--shadow-medium);
}

/* Products Grid */
#products {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 20px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 15px;
}

@media (min-width: 768px) {
  #products {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 0 20px;
  }
}

.product-card {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 12px var(--shadow-soft);
  animation: fadeSlide 0.5s ease both;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  z-index: 10;
  border: 1px solid var(--card-border);
  cursor: pointer;
  overflow: hidden;
}

.product-card:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(138, 138, 138, 0.1), transparent);
  transition: left 0.6s;
}

.product-card:hover:before {
  left: 100%;
}

.product-card.expanded {
  transform: scale(1.05);
  z-index: 100;
  box-shadow: 0 8px 25px var(--shadow-medium);
  background: var(--card-bg-hover);
  border-color: rgba(0, 0, 0, 0.3);
}

.product-card img {
  width: 120px;
  height: 120px;
  object-fit: contain;
  margin-bottom: 15px;
  filter: drop-shadow(0 0 8px rgba(138, 138, 138, 0.3));
  transition: transform 0.3s ease;
}

.product-card:hover img {
  transform: scale(1.05);
}

.product-card h3 {
  margin: 15px 0 10px;
  font-size: 18px;
  color: var(--text-primary);
}

.product-card p {
  color: #FFFFFF; /* أبيض واضح للسعر */
  font-weight: 700; /* خط أثخن */
  font-size: 17px; /* حجم أكبر قليلاً */
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4); /* ظل للنص */
  background: linear-gradient(145deg, var(--cement-medium), var(--cement-dark)); /* خلفية خفيفة */
  padding: 8px 16px; /* حشو داخلي */
  border-radius: 20px; /* حواف دائرية */
  display: inline-block; /* عرض مناسب */
  margin-top: 10px; /* مسافة من الأعلى */
  border: 1px solid rgba(255, 255, 255, 0.1); /* حد خفيف */
}

/* Footer Styles */
.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: linear-gradient(135deg, var(--bg-darkest), var(--bg-dark));
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border-top: 2px solid var(--card-border);
  z-index: 1000;
  backdrop-filter: blur(15px);
}

.footer button {
  background: var(--card-bg);
  color: #FFFFFF; /* أبيض واضح */
  border: 1px solid var(--card-border);
  padding: 10px 16px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px; /* زيادة حجم الخط قليلاً */
  font-weight: 600; /* جعل الخط أثخن */
  min-width: 80px;
  flex: 1;
  max-width: 120px;
  margin: 0 5px;
  box-shadow: 0 2px 6px var(--shadow-soft);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* إضافة ظل للنص */
}

.footer button:hover {
  background: var(--cement-medium);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-medium);
  border-color: rgba(0, 0, 0, 0.3);
}

/* Modal Styles - Enhanced for Mobile */
.modal-glass {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 2000;
  background: rgba(0,0,0,0.75);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease;
  backdrop-filter: blur(8px);
}

.modal-glass.show {
  opacity: 1;
  pointer-events: auto;
}

.modal-content {
  background: linear-gradient(145deg, var(--card-bg), rgba(90, 90, 90, 0.95));
  border-radius: 20px 20px 0 0;
  padding: 25px 20px;
  width: 100%;
  max-width: 500px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 -8px 30px var(--shadow-medium);
  border: 2px solid var(--card-border);
  backdrop-filter: blur(20px);
  color: var(--text-primary);
  position: relative;
  animation: modalSlideUp 0.5s cubic-bezier(.2,.8,.2,1) both;
  /* Custom scrollbar for modal */
  scrollbar-width: thin;
  scrollbar-color: var(--cement-medium) rgba(255,255,255,0.1);
}

.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: var(--cement-medium);
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--cement-light);
}

/* Modal Handle for dragging */
.modal-handle {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background: var(--cement-medium);
  border-radius: 2px;
  cursor: grab;
  transition: background 0.2s ease;
}

.modal-handle:active {
  cursor: grabbing;
  background: var(--cement-light);
}

.close-modal {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(138, 138, 138, 0.2);
  color: var(--cement-medium);
  width: 35px;
  height: 35px;
  border-radius: 50%;
  border: 1px solid var(--card-border);
  font-size: 18px;
  cursor: pointer;
  opacity: 0.8;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-modal:hover {
  background: rgba(138, 138, 138, 0.3);
  opacity: 1;
  transform: scale(1.05);
  border-color: rgba(0, 0, 0, 0.3);
}

@keyframes modalSlideUp {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalSlideDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(100%);
  }
}

/* Rating System */
.stars {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin: 20px 0;
  font-size: 28px;
}

.stars span {
  transition: all 0.2s ease;
  cursor: pointer;
  color: #555;
  user-select: none;
  -webkit-user-select: none;
}

.stars span:hover,
.stars span.active {
  color: var(--cement-medium);
  text-shadow: 0 0 10px rgba(138, 138, 138, 0.6);
  transform: scale(1.2);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 16px;
  color: var(--text-primary);
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border-radius: 10px;
  background: rgba(74, 74, 74, 0.8);
  border: 2px solid var(--card-border);
  color: var(--text-primary);
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--cement-medium);
  box-shadow: 0 0 0 3px rgba(138, 138, 138, 0.2);
  background: rgba(74, 74, 74, 0.95);
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.modal-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
  justify-content: flex-end;
}

.modal-actions button {
  border: none;
  background: var(--cement-medium);
  color: var(--text-primary);
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  box-shadow: 0 3px 10px var(--shadow-soft);
}

.modal-actions button.cancel-btn {
  background: #666;
  color: var(--text-primary);
}

.modal-actions button:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px var(--shadow-medium);
}

.modal-actions button.cancel-btn:hover {
  background: #777;
}

.modal-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Scroll to Top Button */
#toTop {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: var(--cement-medium);
  color: #FFFFFF; /* لون السهم أبيض واضح */
  border: none;
  border-radius: 50%;
  font-size: 20px; /* حجم أكبر للسهم */
  cursor: pointer;
  box-shadow: 0 4px 12px var(--shadow-soft);
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 1000;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* ظل للسهم */
  border: 2px solid rgba(255, 255, 255, 0.2); /* حد خفيف */
}

#toTop.show {
  opacity: 1;
  transform: scale(1);
}

#toTop:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 6px 18px var(--shadow-medium);
  background: var(--cement-light);
  color: #FFFFFF; /* الحفاظ على اللون الأبيض عند الـ hover */
  border-color: rgba(255, 255, 255, 0.3); /* تفتيح الحد عند الـ hover */
}

#toTop:active {
  transform: scale(0.95);
}

/* Social Footer */
.social-footer {
  background: linear-gradient(135deg, var(--bg-darkest) 0%, rgba(47, 47, 47, 0.98) 100%);
  border-top: 1px solid var(--card-border);
  box-shadow: 0 -6px 20px var(--shadow-soft);
  padding: 32px 16px 40px;
  margin-top: 48px;
  position: relative;
  overflow: hidden;
}

.social-footer::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 20% 80%, rgba(138, 138, 138, 0.05) 0%, transparent 50%);
  opacity: 0.3;
  pointer-events: none;
  animation: float 60s linear infinite;
}

.social-container {
  max-width: 420px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.instagram-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 9999px;
  background: rgba(138, 138, 138, 0.15);
  color: var(--cement-medium);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.25s ease;
  border: 1px solid var(--card-border);
}

.instagram-link img {
  width: 20px;
  height: 20px;
  filter: drop-shadow(0 0 3px rgba(138, 138, 138, 0.4));
}

.instagram-link:hover {
  background: rgba(138, 138, 138, 0.25);
  transform: scale(1.03);
  border-color: rgba(0, 0, 0, 0.3);
}

.instagram-link:active {
  transform: scale(0.97);
}

.copyright-text {
  font-size: 0.85rem;
  line-height: 1.6;
  color: var(--text-secondary);
  text-shadow: 0 0 4px rgba(138, 138, 138, 0.2);
}

@media (min-width: 580px) {
  .social-container {
    flex-direction: row;
    justify-content: center;
    gap: 32px;
  }
  .copyright-text { 
    text-align: left; 
  }
}

/* Copyright Styles */
.copyright {
  position: fixed;
  bottom: 10px;
  width: 100%;
  text-align: center;
  font-size: 14px;
  color: var(--cement-medium);
  text-shadow: 0 1px 3px rgba(0,0,0,0.6);
  padding: 8px 0;
  background: rgba(0,0,0,0.4);
  backdrop-filter: blur(4px);
  z-index: 999;
}

/* Additional Animations */
@keyframes slideInDown {
  from {
    transform: translateY(-100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100px);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes fadeSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* Smooth transitions for all interactive elements */
button, .category-card, .product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects */
.category-card:hover {
  transform: scale(1.03) translateY(-3px);
  box-shadow: 0 8px 20px var(--shadow-medium);
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 18px var(--shadow-medium);
}

/* Loading state styles */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--cement-medium);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced focus states for accessibility */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--cement-medium);
  outline-offset: 2px;
}

/* Improved touch targets */
@media (max-width: 768px) {
  button,
  .category-card,
  .product-card {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Fade animations for content switching */
.fade-in {
  animation: fadeSlide 0.5s ease both;
}

.fade-out {
  animation: fadeOut 0.3s ease both;
}

/* Enhanced notification styles */
.notification {
  backdrop-filter: blur(15px);
  border: 1px solid var(--card-border);
}

/* Floating particles with cement theme */
.floating-particle {
  background: rgba(138, 138, 138, 0.3) !important;
  box-shadow: 0 0 4px rgba(138, 138, 138, 0.4);
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-darker);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--cement-medium), var(--cement-dark));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--cement-light), var(--cement-medium));
}

/* Dark theme text selection */
::selection {
  background: rgba(138, 138, 138, 0.3);
  color: var(--text-primary);
}

::-moz-selection {
  background: rgba(138, 138, 138, 0.3);
  color: var(--text-primary);
}

/* Enhanced gradient backgrounds */
body {
  background-attachment: fixed;
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Refined shadow effects */
.category-card,
.product-card {
  box-shadow: 
    0 4px 12px var(--shadow-soft),
    inset 0 1px 0 rgba(255,255,255,0.03);
}

.category-card:hover,
.product-card:hover {
  box-shadow: 
    0 6px 18px var(--shadow-medium),
    0 0 15px rgba(138, 138, 138, 0.15),
    inset 0 1px 0 rgba(255,255,255,0.05);
}

/* Special effects for better visual comfort */
.modal-content h3 {
  color: var(--cement-light);
}

.questions .question p {
  color: var(--text-primary) !important;
}

/* Better contrast for readability */
.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Smooth animations for better user experience */
.category-card,
.product-card,
button {
  will-change: transform;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --card-border: rgba(0, 0, 0, 0.4);
    --shadow-soft: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.5);
  }
  
  .category-card,
  .product-card,
  button {
    border-width: 2px;
  }
}