// Categories Data with Icons
const categoriesData = [
  { 
    key: "Hot", 
    label: "Hot", 
    icon: `<img src="images/coffee.png" alt="Hot Coffee" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Ice", 
    label: "Ice", 
    icon: `<img src="images/cold-coffee.png" alt="Iced Coffee" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Frappe", 
    label: "Frappe", 
    icon: `<img src="images/frappe.png" alt="Frappe" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Refreshing", 
    label: "Refreshing", 
    icon: `<img src="images/refreshment.png" alt="Refreshing Drinks" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Juices", 
    label: "Juices", 
    icon: `<img src="images/juices.png" alt="Fresh Juices" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Milkshakes", 
    label: "Milkshakes", 
    icon: `<img src="images/chocolate.png" alt="Milkshakes" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Smoothies", 
    label: "Smoothies", 
    icon: `<img src="images/smoothie.png" alt="Smoothies" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Donuts", 
    label: "Donuts", 
    icon: `<img src="images/donut.png" alt="Donuts" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Cake", 
    label: "Cake", 
    icon: `<img src="images/cake.png" alt="Cakes" style="width:56px;height:56px;margin-bottom:12px">`
  }
];

// Products Data
const productsData = {
  Hot: [
    { name: "Espresso", price: "$2.50", img: "logo" },
    { name: "Latte", price: "$3.00", img: "logo" },
    { name: "Cappuccino", price: "$3.20", img: "logo" },
    { name: "Americano", price: "$2.80", img: "logo" },
    { name: "Macchiato", price: "$3.40", img: "logo" },
    { name: "Turkish Coffee", price: "$2.20", img: "logo" }
  ],
  Ice: [
    { name: "Iced Americano", price: "$3.20", img: "logo" },
    { name: "Cold Brew", price: "$3.50", img: "logo" },
    { name: "Iced Latte", price: "$3.80", img: "logo" },
    { name: "Iced Mocha", price: "$4.00", img: "logo" },
    { name: "Iced Cappuccino", price: "$3.60", img: "logo" },
    { name: "Nitro Coffee", price: "$4.20", img: "logo" }
  ],
  Frappe: [
    { name: "Caramel Frappe", price: "$3.80", img: "logo" },
    { name: "Mocha Frappe", price: "$4.00", img: "logo" },
    { name: "Vanilla Frappe", price: "$3.70", img: "logo" },
    { name: "Hazelnut Frappe", price: "$3.90", img: "logo" },
    { name: "Cookie Frappe", price: "$4.10", img: "logo" }
  ],
  Refreshing: [
    { name: "Lemon Mint", price: "$2.80", img: "logo" },
    { name: "Cucumber Water", price: "$2.50", img: "logo" },
    { name: "Green Tea", price: "$2.00", img: "logo" },
    { name: "Iced Tea", price: "$2.30", img: "logo" },
    { name: "Mint Lemonade", price: "$3.00", img: "logo" }
  ],
  Juices: [
    { name: "Orange Juice", price: "$2.50", img: "logo" },
    { name: "Apple Juice", price: "$2.30", img: "logo" },
    { name: "Mixed Berry", price: "$3.00", img: "logo" },
    { name: "Mango Juice", price: "$2.80", img: "logo" },
    { name: "Pineapple Juice", price: "$2.70", img: "logo" },
    { name: "Watermelon Juice", price: "$2.60", img: "logo" }
  ],
  Milkshakes: [
    { name: "Chocolate Shake", price: "$4.20", img: "logo" },
    { name: "Vanilla Shake", price: "$4.00", img: "logo" },
    { name: "Strawberry Shake", price: "$4.10", img: "logo" },
    { name: "Oreo Shake", price: "$4.50", img: "logo" },
    { name: "Banana Shake", price: "$3.90", img: "logo" }
  ],
  Smoothies: [
    { name: "Berry Smoothie", price: "$4.00", img: "logo" },
    { name: "Mango Smoothie", price: "$4.20", img: "logo" },
    { name: "Green Smoothie", price: "$4.50", img: "logo" },
    { name: "Tropical Smoothie", price: "$4.30", img: "logo" },
    { name: "Protein Smoothie", price: "$4.80", img: "logo" }
  ],
  Donuts: [
    { name: "Glazed Donut", price: "$1.50", img: "logo" },
    { name: "Chocolate Donut", price: "$1.80", img: "logo" },
    { name: "Jelly Donut", price: "$2.00", img: "logo" },
    { name: "Boston Cream", price: "$2.20", img: "logo" },
    { name: "Cinnamon Sugar", price: "$1.70", img: "logo" }
  ],
  Cake: [
    { name: "Chocolate Cake", price: "$4.50", img: "logo" },
    { name: "Cheesecake", price: "$5.00", img: "logo" },
    { name: "Red Velvet", price: "$4.80", img: "logo" },
    { name: "Carrot Cake", price: "$4.30", img: "logo" },
    { name: "Tiramisu", price: "$5.20", img: "logo" },
    { name: "Black Forest", price: "$4.90", img: "logo" }
  ]
};

// Global Variables - Optimized
let modalTouchStart = 0;
let modalCurrentTranslate = 0;
let modalIsDragging = false;

// Performance optimized global state
const appState = {
  isScrolling: false,
  currentCategory: null,
  particlesInitialized: false,
  slideshowInitialized: false
};

// DOM Elements - Cached for better performance
const domElements = {};

// Critical: Initialize DOM cache early
function initDOMCache() {
  domElements.categoryList = document.getElementById("categoryList");
  domElements.categoryBar = document.getElementById("categoryBar");
  domElements.productsGrid = document.getElementById("products");
  domElements.topBar = document.getElementById("topBar");
  domElements.backBtn = document.getElementById("backBtn");
  domElements.logoImg = document.querySelector(".logo");
  domElements.progressFill = document.querySelector('.progress-fill');
  domElements.progressText = document.querySelector('.progress-text');
  domElements.toTop = document.getElementById("toTop");
  domElements.waterGallery = document.getElementById("water-gallery");

  // Initialize mobile-specific optimizations
  if (window.innerWidth <= 768) {
    initMobileOptimizations();
  }
}

// Mobile-specific optimizations
function initMobileOptimizations() {
  // Ensure scroll to top button is properly positioned
  if (domElements.toTop) {
    domElements.toTop.style.bottom = '90px';
    domElements.toTop.style.right = '15px';
  }

  // Add touch-friendly classes to navigation elements
  if (domElements.topBar) {
    domElements.topBar.classList.add('mobile-optimized');
  }

  if (domElements.categoryBar) {
    domElements.categoryBar.classList.add('mobile-scroll');
  }
}

// Performance optimized progress bar
function updateProgress(percent) {
  if (!domElements.progressFill || !domElements.progressText) return;
  
  // Use requestAnimationFrame for smooth updates
  requestAnimationFrame(() => {
    domElements.progressFill.style.width = `${percent}%`;
    domElements.progressText.textContent = `${Math.round(percent)}%`;
    
    if (percent < 30) {
      domElements.progressFill.style.background = '#ff5722';
    } else if (percent < 70) {
      domElements.progressFill.style.background = '#ff9800';
    } else {
      domElements.progressFill.style.background = 'linear-gradient(90deg, #DAA520, #FFD700)';
      domElements.progressFill.style.backgroundSize = '200% 100%';
      domElements.progressFill.style.animation = 'shimmer 2s infinite';
    }
  });
}

function animateProgress(duration = 3000) {
  let start = null;
  const max = 100;
  
  function step(timestamp) {
    if (!start) start = timestamp;
    const progress = Math.min((timestamp - start) / duration * max, max);
    updateProgress(progress);
    if (progress < max) {
      requestAnimationFrame(step);
    } else {
      setTimeout(() => {
        const progressBar = document.getElementById('demoProgress');
        if (progressBar) {
          progressBar.style.opacity = '0';
          setTimeout(() => {
            progressBar.style.display = 'none';
          }, 500);
        }
      }, 500);
    }
  }
  requestAnimationFrame(step);
}

// Optimized intro animation with error handling
window.addEventListener("load", () => {
  try {
    // Initialize DOM cache first
    initDOMCache();
    
    animateProgress();
    
    setTimeout(() => {
      const intro = document.getElementById("intro");
      const main = document.getElementById("main");
      const progressBar = document.getElementById('demoProgress');

      if (!intro || !main) {
        console.warn('Essential elements not found, falling back to safe mode');
        showMainContent();
        return;
      }

      intro.style.opacity = 0;

      setTimeout(() => {
        intro.style.display = "none";
        if (progressBar) progressBar.style.display = "none";
        showMainContent();
        
        // Initialize features in order of priority
        requestAnimationFrame(() => {
          initSlideshow();
          renderCategoryCards();
          // Delay particles to improve initial load performance
          setTimeout(() => {
            createFloatingParticles();
          }, 1000);
        });
      }, 1000);
    }, 3500);
  } catch (e) {
    console.error('Load error:', e);
    showMainContent(); // Fallback
  }
});

// Safe fallback function
function showMainContent() {
  const main = document.getElementById("main");
  if (main) {
    main.style.display = "block";
    document.body.style.overflow = "auto";
  }
}

// Optimized category cards rendering
function renderCategoryCards() {
  if (!domElements.categoryList) return;
  
  domElements.categoryList.classList.remove("fade-in", "fade-out");
  domElements.categoryList.innerHTML = "";
  domElements.categoryList.style.display = "grid";
  domElements.categoryList.style.opacity = "1";
  
  if (domElements.topBar) domElements.topBar.style.display = "none";
  if (domElements.productsGrid) domElements.productsGrid.style.display = "none";
  
  // Use document fragment for better performance
  const fragment = document.createDocumentFragment();
  
  categoriesData.forEach((cat, index) => {
    const div = document.createElement("div");
    div.className = `category-card cat-${cat.key.toLowerCase()}`;
    div.innerHTML = `
      <span class="cat-icon">${cat.icon}</span>
      <h3 class="cat-label">${cat.label}</h3>
    `;
    
    const delay = (Math.floor(index / 2) * 0.1) + ((index % 2) * 0.05);
    div.style.animationDelay = `${delay}s`;
    
    // Optimized event handlers
    div.addEventListener('click', () => showCategory(cat.key), { passive: true });
    
    // Touch feedback with debouncing
    let touchTimer;
    div.addEventListener('touchstart', () => {
      clearTimeout(touchTimer);
      div.style.transform = 'scale(0.95)';
    }, { passive: true });
    
    div.addEventListener('touchend', () => {
      touchTimer = setTimeout(() => {
        div.style.transform = '';
      }, 150);
    }, { passive: true });
    
    fragment.appendChild(div);
  });
  
  domElements.categoryList.appendChild(fragment);
}

// Optimized category display function
function showCategory(cat) {
  appState.currentCategory = cat;
  
  if (domElements.logoImg) domElements.logoImg.style.display = "none";
  if (domElements.waterGallery) domElements.waterGallery.style.display = "none";
  
  // Optimize active class update
  const activeCard = domElements.categoryList.querySelector('.active');
  if (activeCard) activeCard.classList.remove('active');
  
  const idx = categoriesData.findIndex(c => c.key === cat);
  if (idx !== -1 && domElements.categoryList.children[idx]) {
    domElements.categoryList.children[idx].classList.add("active");
  }

  fadeHide(domElements.categoryList, () => {
    if (domElements.topBar) domElements.topBar.style.display = "block";
    renderCategoryBar(cat);
    renderProducts(cat);
    fadeShow(domElements.productsGrid);
  });
}

// Optimized products rendering
function renderProducts(cat) {
  if (!domElements.productsGrid) return;
  
  domElements.productsGrid.innerHTML = "";
  const items = productsData[cat];
  
  if (!items || !items.length) {
    domElements.productsGrid.innerHTML = "<p style='text-align:center;color:#ccc;grid-column:1/-1;padding:2rem;'>No products in this category yet.</p>";
    return;
  }

  // Use document fragment
  const fragment = document.createDocumentFragment();
  
  items.forEach((item, i) => {
    const card = document.createElement("div");
    card.className = "product-card";
    card.style.animationDelay = `${i * 0.1}s`;
    card.innerHTML = `
      <img src="images/logo.png" alt="${item.name}" style="width: 120px; height: 120px; object-fit: contain; margin-bottom: 15px; filter: drop-shadow(0 0 10px rgba(218, 165, 32, 0.3)); transition: transform 0.3s ease;">
      <h3>${item.name}</h3>
      <p>${item.price}</p>
    `;
    
    // Optimized click handler with debouncing
    let clickTimer;
    card.addEventListener('click', function(e) {
      e.stopPropagation();
      
      clearTimeout(clickTimer);
      clickTimer = setTimeout(() => {
        // Remove expanded state from other cards
        document.querySelectorAll('.product-card.expanded').forEach(otherCard => {
          if (otherCard !== card) {
            otherCard.classList.remove('expanded');
          }
        });
        
        // Toggle expanded state
        card.classList.toggle('expanded');
        
        // Create ripple effect
        createRippleEffect(card, e);
      }, 50); // Small debounce to prevent double clicks
      
    }, { passive: true });
    
    fragment.appendChild(card);
  });
  
  domElements.productsGrid.appendChild(fragment);
}

// Optimized category bar rendering
function renderCategoryBar(activeCat) {
  if (!domElements.categoryBar) return;
  
  domElements.categoryBar.innerHTML = "";
  const fragment = document.createDocumentFragment();
  
  categoriesData.forEach((cat) => {
    if (cat.key === activeCat) return;
    const btn = document.createElement("button");
    btn.textContent = cat.label;
    btn.addEventListener('click', () => showCategory(cat.key), { passive: true });
    fragment.appendChild(btn);
  });
  
  domElements.categoryBar.appendChild(fragment);
}

// Optimized back button handler
if (domElements.backBtn) {
  domElements.backBtn.addEventListener('click', () => {
    if (domElements.logoImg) domElements.logoImg.style.display = "block";
    if (domElements.waterGallery) domElements.waterGallery.style.display = "block";
    
    fadeHide(domElements.productsGrid, () => {
      if (domElements.topBar) domElements.topBar.style.display = "none";
      renderCategoryCards();
      fadeShow(domElements.categoryList);
    });
  }, { passive: true });
}

// Optimized slideshow with better performance
function initSlideshow() {
  if (appState.slideshowInitialized) return;
  
  const slides = document.querySelectorAll('.slide');
  if (!slides.length) return;
  
  let currentSlide = 0;
  
  function showSlide(n) {
    // Use RAF for smooth transitions
    requestAnimationFrame(() => {
      slides.forEach(slide => slide.classList.remove('fade'));
      if (slides[n]) {
        slides[n].classList.add('fade');
      }
    });
  }
  
  function nextSlide() {
    currentSlide = (currentSlide + 1) % slides.length;
    showSlide(currentSlide);
  }
  
  showSlide(0);
  setInterval(nextSlide, 5000);
  appState.slideshowInitialized = true;
}

// Optimized fade functions
const ANIM_DURATION = 150;

function fadeShow(el, duration = ANIM_DURATION) {
  if (!el) return;
  
  requestAnimationFrame(() => {
    el.style.display = '';          
    el.classList.remove('fade-out');
    el.classList.add('fade-in');
    setTimeout(() => el.classList.remove('fade-in'), duration);
  });
}

function fadeHide(el, cb, duration = ANIM_DURATION) {
  if (!el) return;

  requestAnimationFrame(() => {
    el.classList.remove('fade-in');
    el.classList.add('fade-out');
    
    if (typeof cb === 'function') cb();
    
    setTimeout(() => {
      el.style.display = 'none';
      el.classList.remove('fade-out');
    }, duration);
  });
}

// Enhanced Modal System with Touch Support - Optimized
function initModalTouchSupport() {
  const modals = document.querySelectorAll('.modal-glass');
  
  modals.forEach(modal => {
    const modalContent = modal.querySelector('.modal-content');
    const modalHandle = modal.querySelector('.modal-handle');
    
    if (!modalHandle || !modalContent) return;
    
    let startY = 0;
    let currentY = 0;
    let isDragging = false;
    
    // Optimized touch handlers with passive events where possible
    modalHandle.addEventListener('touchstart', handleTouchStart, { passive: false });
    modalContent.addEventListener('touchmove', handleTouchMove, { passive: false });
    modalContent.addEventListener('touchend', handleTouchEnd, { passive: true });
    
    // Mouse events for desktop
    modalHandle.addEventListener('mousedown', handleMouseDown);
    
    function handleTouchStart(e) {
      startY = e.touches[0].clientY;
      isDragging = true;
      modalContent.style.transition = 'none';
      modalContent.style.userSelect = 'none';
    }
    
    function handleTouchMove(e) {
      if (!isDragging) return;
      
      currentY = e.touches[0].clientY;
      const deltaY = currentY - startY;
      
      if (deltaY > 0) { // Only allow dragging down
        e.preventDefault();
        // Use transform3d for better performance
        modalContent.style.transform = `translate3d(0, ${deltaY}px, 0)`;
        
        // Add opacity effect based on drag distance
        const opacity = Math.max(0.3, 1 - (deltaY / 400));
        modal.style.background = `rgba(0,0,0,${0.8 * opacity})`;
      }
    }
    
    function handleTouchEnd() {
      if (!isDragging) return;
      
      isDragging = false;
      modalContent.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
      modalContent.style.userSelect = '';
      
      const deltaY = currentY - startY;
      
      // If dragged more than 120px, close the modal
      if (deltaY > 120) {
        closeModal(modal);
      } else {
        // Snap back to original position
        modalContent.style.transform = 'translate3d(0, 0, 0)';
        modal.style.background = 'rgba(0,0,0,0.8)';
      }
      
      startY = 0;
      currentY = 0;
    }
    
    function handleMouseDown(e) {
      startY = e.clientY;
      isDragging = true;
      modalContent.style.transition = 'none';
      e.preventDefault();
      
      function handleMouseMove(e) {
        if (!isDragging) return;
        
        currentY = e.clientY;
        const deltaY = currentY - startY;
        
        if (deltaY > 0) {
          modalContent.style.transform = `translate3d(0, ${deltaY}px, 0)`;
          const opacity = Math.max(0.3, 1 - (deltaY / 400));
          modal.style.background = `rgba(0,0,0,${0.8 * opacity})`;
        }
      }
      
      function handleMouseUp() {
        if (!isDragging) return;
        
        isDragging = false;
        modalContent.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
        
        const deltaY = currentY - startY;
        
        if (deltaY > 120) {
          closeModal(modal);
        } else {
          modalContent.style.transform = 'translate3d(0, 0, 0)';
          modal.style.background = 'rgba(0,0,0,0.8)';
        }
        
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        
        startY = 0;
        currentY = 0;
      }
      
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  });
}

// Enhanced Modal Functions - Optimized
function showModal(modal) {
  if (!modal) return;
  
  requestAnimationFrame(() => {
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
    
    // Reset modal position
    const modalContent = modal.querySelector('.modal-content');
    if (modalContent) {
      modalContent.style.transform = 'translate3d(0, 0, 0)';
      modalContent.style.animation = 'modalSlideUp 0.5s cubic-bezier(.2,.8,.2,1) both';
    }
    modal.style.background = 'rgba(0,0,0,0.8)';
  });
}

function closeModal(modal) {
  if (!modal) return;
  
  const modalContent = modal.querySelector('.modal-content');
  
  if (modalContent) {
    modalContent.style.animation = 'modalSlideDown 0.3s ease-out both';
  }
  
  setTimeout(() => {
    modal.classList.remove('show');
    document.body.style.overflow = 'auto';
    if (modalContent) {
      modalContent.style.animation = '';
      modalContent.style.transform = '';
    }
  }, 300);
}

// Enhanced Notification System - Optimized
function showNotification(message, type = 'info') {
  // Remove any existing notifications efficiently
  const existingNotifications = document.querySelectorAll('.notification');
  existingNotifications.forEach(notif => {
    if (document.body.contains(notif)) {
      document.body.removeChild(notif);
    }
  });
  
  const notification = document.createElement('div');
  notification.className = 'notification';
  
  // Pre-calculate styles for better performance
  const backgroundColor = type === 'error' ? 
    'linear-gradient(145deg, #ff4444, #cc0000)' : 
    'linear-gradient(145deg, #DAA520, #B8860B)';
    
  notification.style.cssText = `
    position: fixed;
    top: 80px;
    right: 15px;
    left: 15px;
    max-width: 350px;
    margin: 0 auto;
    background: ${backgroundColor};
    color: white;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    z-index: 3000;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    word-wrap: break-word;
    animation: slideInDown 0.4s cubic-bezier(.2,.8,.2,1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    cursor: pointer;
    user-select: none;
  `;
  
  // Add icon based on type
  const icon = type === 'error' ? '⚠️' : '✅';
  notification.innerHTML = `<span style="margin-right: 8px;">${icon}</span>${message}`;
  
  document.body.appendChild(notification);
  
  // Auto remove after 4 seconds
  const autoRemove = setTimeout(() => {
    removeNotification(notification);
  }, 4000);
  
  // Add click to dismiss with optimized event handling
  notification.addEventListener('click', () => {
    clearTimeout(autoRemove);
    removeNotification(notification);
  }, { passive: true, once: true });
  
  function removeNotification(notif) {
    if (!document.body.contains(notif)) return;
    
    notif.style.animation = 'slideOutUp 0.3s ease-out forwards';
    setTimeout(() => {
      if (document.body.contains(notif)) {
        document.body.removeChild(notif);
      }
    }, 300);
  }
}

// Optimized footer button animation
function animateFooterButton(clickedId) {
  const buttons = ['mainMenuBtn', 'rateUsBtn', 'aboutUsBtn'];
  
  requestAnimationFrame(() => {
    buttons.forEach(id => {
      const btn = document.getElementById(id);
      if (!btn) return;
      
      if (id === clickedId) {
        btn.style.transform = 'translateY(-8px) scale(1.05)';
        btn.style.transition = 'transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
        setTimeout(() => {
          btn.style.transform = 'translateY(0) scale(1)';
        }, 300);
      } else {
        btn.style.transform = 'none';
      }
    });
  });
}

// Optimized floating particles with performance controls
function createFloatingParticles() {
  if (appState.particlesInitialized) return;
  
  // Reduce particles on mobile for better performance
  const isMobile = window.innerWidth < 768;
  const particleCount = isMobile ? 15 : 25;
  
  // Clean up existing particles
  const existingParticles = document.querySelectorAll('.floating-particle');
  existingParticles.forEach(p => p.remove());
  
  // Use batch creation for better performance
  const fragment = document.createDocumentFragment();
  
  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    particle.className = 'floating-particle';
    
    // Pre-calculate random values
    const size = Math.random() * 4 + 2;
    const opacity = Math.random() * 0.5 + 0.2;
    const leftPos = Math.random() * 100;
    const topPos = Math.random() * 100;
    const duration = Math.random() * 10 + 15;
    
    particle.style.cssText = `
      position: fixed;
      width: ${size}px;
      height: ${size}px;
      background: rgba(138, 138, 138, ${opacity});
      border-radius: 50%;
      pointer-events: none;
      z-index: 1;
      left: ${leftPos}vw;
      top: ${topPos}vh;
      animation: float ${duration}s linear infinite;
      will-change: transform, opacity;
    `;
    fragment.appendChild(particle);
  }
  
  document.body.appendChild(fragment);
  appState.particlesInitialized = true;
}

// Enhanced product card interactions
function enhanceProductCards() {
  // Use event delegation for better performance
  document.addEventListener('click', (e) => {
    if (e.target.closest('.product-card')) {
      return; // Let the card handle its own click
    } else {
      // Remove expanded state when clicking outside
      const expandedCards = document.querySelectorAll('.product-card.expanded');
      if (expandedCards.length > 0) {
        requestAnimationFrame(() => {
          expandedCards.forEach(card => {
            card.classList.remove('expanded');
          });
        });
      }
    }
  }, { passive: true });
}

// Optimized ripple effect
function createRippleEffect(element, event) {
  // Remove existing ripples first
  const existingRipples = element.querySelectorAll('.ripple');
  existingRipples.forEach(r => r.remove());
  
  const ripple = document.createElement('div');
  ripple.className = 'ripple';
  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = event.clientX - rect.left - size / 2;
  const y = event.clientY - rect.top - size / 2;
  
  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    left: ${x}px;
    top: ${y}px;
    background: rgba(218, 165, 32, 0.3);
    border-radius: 50%;
    pointer-events: none;
    transform: scale(0);
    animation: ripple 0.6s ease-out;
    z-index: 1;
  `;
  
  element.style.position = 'relative';
  element.appendChild(ripple);
  
  setTimeout(() => {
    if (ripple.parentNode) {
      ripple.remove();
    }
  }, 600);
}

// Highly optimized scroll handler - Fixed performance issues
let scrollTimeout = null;
let isScrollHandlerRunning = false;
let lastScrollY = 0;

function handleScroll() {
  if (isScrollHandlerRunning) return;
  isScrollHandlerRunning = true;
  
  requestAnimationFrame(() => {
    const scrolled = window.scrollY;
    
    // Only update if scroll position changed significantly
    if (Math.abs(scrolled - lastScrollY) < 5) {
      isScrollHandlerRunning = false;
      return;
    }
    
    lastScrollY = scrolled;
    
    // Handle scroll to top button - Enhanced for mobile
    if (domElements.toTop) {
      // Adjust threshold based on device type
      const isMobile = window.innerWidth <= 768;
      const scrollThreshold = isMobile ? 300 : 400;

      if (scrolled > scrollThreshold) {
        if (!domElements.toTop.classList.contains("show")) {
          domElements.toTop.style.display = "block";
          // Add small delay for better mobile performance
          requestAnimationFrame(() => {
            domElements.toTop.classList.add("show");
          });
        }
      } else {
        if (domElements.toTop.classList.contains("show")) {
          domElements.toTop.classList.remove("show");
          setTimeout(() => {
            if (!domElements.toTop.classList.contains("show")) {
              domElements.toTop.style.display = "none";
            }
          }, isMobile ? 200 : 300);
        }
      }
    }
    
    // Disable parallax on mobile for better performance
    if (window.innerWidth >= 768 && domElements.waterGallery && 
        domElements.waterGallery.style.display !== 'none' && scrolled < 1000) {
      const rate = scrolled * -0.02; // Reduced intensity for better performance
      domElements.waterGallery.style.transform = `translate3d(0, ${rate}px, 0)`;
    }
    
    isScrollHandlerRunning = false;
  });
}

// Throttled scroll event listener with better performance
function throttledScrollHandler() {
  if (scrollTimeout) return;
  
  scrollTimeout = setTimeout(() => {
    handleScroll();
    scrollTimeout = null;
  }, 16); // ~60fps
}

// Add optimized scroll event listener
window.addEventListener('scroll', throttledScrollHandler, { passive: true });

// Enhanced scroll to top function with better mobile support
function scrollToTop() {
  const isMobile = window.innerWidth <= 768;
  const hasNativeSmooth = 'scrollBehavior' in document.documentElement.style;

  // Add haptic feedback on mobile if available
  if (isMobile && window.navigator && window.navigator.vibrate) {
    window.navigator.vibrate(50);
  }

  // Use native smooth scrolling on desktop and modern mobile browsers
  if (hasNativeSmooth && !isMobile) {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  } else {
    // Custom smooth scrolling optimized for mobile
    const startY = window.scrollY;
    const duration = isMobile ? 600 : 800; // Faster on mobile
    const startTime = performance.now();

    function easeOutCubic(t) {
      return 1 - Math.pow(1 - t, 3);
    }

    function animateScroll(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easeProgress = easeOutCubic(progress);

      const currentY = startY * (1 - easeProgress);
      window.scrollTo(0, currentY);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    }

    requestAnimationFrame(animateScroll);
  }
}

// Initialize scroll to top listeners with enhanced mobile support
function initScrollToTopListeners() {
  if (domElements.toTop) {
    // Enhanced click handler with debouncing
    let clickTimeout;
    const handleToTopClick = (e) => {
      e.preventDefault();
      e.stopPropagation();

      clearTimeout(clickTimeout);
      clickTimeout = setTimeout(() => {
        scrollToTop();
      }, 50); // Small debounce to prevent double clicks
    };

    domElements.toTop.addEventListener('click', handleToTopClick, { passive: false });

    // Add touch feedback for mobile
    if (window.innerWidth <= 768) {
      domElements.toTop.addEventListener('touchstart', (e) => {
        e.preventDefault();
        domElements.toTop.style.transform = 'scale(0.9)';
      }, { passive: false });

      domElements.toTop.addEventListener('touchend', (e) => {
        e.preventDefault();
        setTimeout(() => {
          domElements.toTop.style.transform = '';
        }, 100);
      }, { passive: false });
    }
  }

  // Add listeners for category bar and back button with scroll to top
  if (domElements.categoryBar) {
    domElements.categoryBar.addEventListener("click", function(e) {
      if (e.target.tagName === "BUTTON") {
        // Small delay to allow category change animation
        setTimeout(() => {
          scrollToTop();
        }, 100);
      }
    }, { passive: true });
  }

  if (domElements.backBtn) {
    domElements.backBtn.addEventListener("click", function() {
      // Small delay to allow back navigation animation
      setTimeout(() => {
        scrollToTop();
      }, 100);
    }, { passive: true });
  }
}

// Main DOMContentLoaded Event - Optimized
window.addEventListener('DOMContentLoaded', function() {
  // Initialize DOM cache if not already done
  if (!domElements.categoryList) {
    initDOMCache();
  }
  
  // Initialize modal touch support
  initModalTouchSupport();
  
  // Initialize enhanced interactions
  enhanceProductCards();
  
  // Initialize scroll listeners
  initScrollToTopListeners();
  
  // Main Menu Button
  const mainMenuBtn = document.getElementById("mainMenuBtn");
  if (mainMenuBtn) {
    mainMenuBtn.addEventListener("click", () => {
      animateFooterButton('mainMenuBtn');
      
      requestAnimationFrame(() => {
        const mainSection = document.getElementById("main");
        if (mainSection) {
          mainSection.style.display = "block";
        }
        if (domElements.waterGallery) domElements.waterGallery.style.display = "block";
        if (domElements.logoImg) domElements.logoImg.style.display = "block";
        
        renderCategoryCards();
        
        // Close any open modals
        document.querySelectorAll('.modal-glass.show').forEach(modal => {
          closeModal(modal);
        });
      });
    }, { passive: true });
  }

  // Rate Us Modal - Optimized
  const rateUsBtn = document.getElementById("rateUsBtn");
  const rateModal = document.getElementById("rateModal");
  if (rateUsBtn && rateModal) {
    rateUsBtn.addEventListener('click', () => {
      animateFooterButton('rateUsBtn');
      showModal(rateModal);
    }, { passive: true });
  }
  
  // Modal close buttons
  const cancelRate = document.getElementById("cancelRate");
  const cancelRate2 = document.getElementById("cancelRate2");
  if (cancelRate) {
    cancelRate.addEventListener('click', () => closeModal(rateModal), { passive: true });
  }
  if (cancelRate2) {
    cancelRate2.addEventListener('click', () => closeModal(rateModal), { passive: true });
  }

  // About Us Modal
  const aboutUsBtn = document.getElementById("aboutUsBtn");
  const aboutModal = document.getElementById("aboutModal");
  if (aboutUsBtn && aboutModal) {
    aboutUsBtn.addEventListener('click', () => {
      animateFooterButton('aboutUsBtn');
      showModal(aboutModal);
    }, { passive: true });
  }
  
  const closeAbout = document.getElementById("closeAbout");
  if (closeAbout) {
    closeAbout.addEventListener('click', () => closeModal(aboutModal), { passive: true });
  }

  // Enhanced Rating System with better performance
  initRatingSystem();

  // Enhanced modal closing - optimized
  document.querySelectorAll('.modal-glass').forEach(modal => {
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal(modal);
      }
    }, { passive: true });
  });
  
  // Escape key to close modals
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      const openModal = document.querySelector('.modal-glass.show');
      if (openModal) {
        closeModal(openModal);
      }
    }
  }, { passive: true });
});

// Optimized rating system initialization
function initRatingSystem() {
  const starContainers = document.querySelectorAll("#rateModal .stars");
  const ratings = {};
  
  starContainers.forEach(container => {
    const question = container.dataset.question;
    ratings[question] = 0;
    container.innerHTML = "";
    
    // Create stars with document fragment
    const fragment = document.createDocumentFragment();
    
    for (let i = 1; i <= 5; i++) {
      const star = document.createElement("span");
      star.textContent = "★";
      star.dataset.rating = i;
      star.style.cssText = `
        cursor: pointer;
        font-size: 24px;
        color: #444;
        transition: all 0.2s ease;
        user-select: none;
        -webkit-user-select: none;
        -webkit-tap-highlight-color: transparent;
      `;
      
      // Optimized event handlers
      let touchTimer;
      
      // Touch events for mobile with debouncing
      star.addEventListener("touchstart", (e) => {
        e.preventDefault();
        clearTimeout(touchTimer);
        highlightStars(container, i);
      }, { passive: false });
      
      star.addEventListener("touchend", (e) => {
        e.preventDefault();
        touchTimer = setTimeout(() => {
          ratings[question] = i;
          highlightStars(container, i);
          
          // Haptic feedback if available
          if (window.navigator && window.navigator.vibrate) {
            window.navigator.vibrate(50);
          }
        }, 50);
      }, { passive: false });
      
      // Mouse events for desktop
      star.addEventListener("mouseenter", () => {
        highlightStars(container, i);
      }, { passive: true });
      
      star.addEventListener("mouseleave", () => {
        highlightStars(container, ratings[question]);
      }, { passive: true });
      
      star.addEventListener("click", (e) => {
        e.preventDefault();
        ratings[question] = i;
        highlightStars(container, i);
      }, { passive: false });
      
      fragment.appendChild(star);
    }
    
    container.appendChild(fragment);
  });

  function highlightStars(container, count) {
    requestAnimationFrame(() => {
      Array.from(container.children).forEach((star, index) => {
        if (index < count) {
          star.style.color = "#DAA520";
          star.style.transform = "scale(1.2)";
          star.style.textShadow = "0 0 15px rgba(218, 165, 32, 0.7)";
        } else {
          star.style.color = "#444";
          star.style.transform = "scale(1)";
          star.style.textShadow = "none";
        }
      });
    });
  }

  // Enhanced form submission with better validation
  const sendRateBtn = document.getElementById("sendRate");
  if (sendRateBtn) {
    sendRateBtn.addEventListener("click", () => {
      handleRatingSubmission(ratings, starContainers, highlightStars, sendRateBtn, rateModal);
    }, { passive: true });
  }
}

// Separated rating submission handler for better organization
function handleRatingSubmission(ratings, starContainers, highlightStars, sendRateBtn, rateModal) {
  const nameInput = document.getElementById("rateName");
  const emailInput = document.getElementById("rateEmail");
  const feedbackInput = document.getElementById("rateOpinion");
  
  const name = nameInput?.value.trim() || "";
  const email = emailInput?.value.trim() || "";
  const feedback = feedbackInput?.value.trim() || "";
  
  // Enhanced validation
  if (!name) {
    showNotification("Please enter your name", "error");
    nameInput?.focus();
    return;
  }
  
  if (name.length < 2) {
    showNotification("Name must be at least 2 characters", "error");
    nameInput?.focus();
    return;
  }
  
  if (!email) {
    showNotification("Please enter your email", "error");
    emailInput?.focus();
    return;
  }
  
  // Email validation
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(email)) {
    showNotification("Please enter a valid email address", "error");
    emailInput?.focus();
    return;
  }
  
  // Check if at least one rating is given
  const hasRating = Object.values(ratings).some(rating => rating > 0);
  if (!hasRating) {
    showNotification("Please give at least one rating", "error");
    return;
  }
  
  // Calculate average rating
  const totalRatings = Object.values(ratings).filter(r => r > 0);
  const averageRating = totalRatings.length > 0 ? 
    (totalRatings.reduce((sum, rating) => sum + rating, 0) / totalRatings.length).toFixed(1) : 0;
  
  // Collect all ratings
  const ratingData = {
    name,
    email,
    feedback,
    ratings,
    averageRating,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    language: navigator.language
  };
  
  console.log("Rating submitted:", ratingData);
  
  // Show success message
  showNotification(`Thank you ${name}! Your rating (${averageRating}★) has been submitted successfully! 🎉`, "success");
  
  // Add loading state
  const originalText = sendRateBtn.textContent;
  sendRateBtn.textContent = "Submitting...";
  sendRateBtn.disabled = true;
  sendRateBtn.style.opacity = "0.6";
  
  // Simulate sending
  setTimeout(() => {
    sendRateBtn.textContent = originalText;
    sendRateBtn.disabled = false;
    sendRateBtn.style.opacity = "1";
    closeModal(rateModal);
    
    // Clear form
    if (nameInput) nameInput.value = "";
    if (emailInput) emailInput.value = "";
    if (feedbackInput) feedbackInput.value = "";
    
    Object.keys(ratings).forEach(key => ratings[key] = 0);
    starContainers.forEach(container => highlightStars(container, 0));
  }, 1500);
}

// Performance monitoring and cleanup
let particleRefreshTimer;

// Initialize particles refresh timer with cleanup
function initParticleTimer() {
  // Clear existing timer
  if (particleRefreshTimer) {
    clearInterval(particleRefreshTimer);
  }
  
  particleRefreshTimer = setInterval(() => {
    const particles = document.querySelectorAll('.floating-particle');
    if (particles.length < (window.innerWidth < 768 ? 10 : 15)) {
      appState.particlesInitialized = false;
      createFloatingParticles();
    }
  }, 30000);
}

// Initialize particle timer
initParticleTimer();

// Error handling and debugging
window.addEventListener('error', (e) => {
  console.error('JavaScript Error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled Promise Rejection:', e.reason);
});

// Performance monitoring
if ('performance' in window) {
  window.addEventListener('load', () => {
    setTimeout(() => {
      try {
        const perfData = performance.getEntriesByType('navigation')[0];
        console.log('Page Load Performance:', {
          loadTime: perfData.loadEventEnd - perfData.loadEventStart,
          domReady: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
          totalTime: perfData.loadEventEnd - perfData.fetchStart
        });
      } catch (e) {
        console.log('Performance monitoring not available');
      }
    }, 1000);
  });
}

// Cleanup function for better memory management
function cleanup() {
  // Clear timers
  if (particleRefreshTimer) {
    clearInterval(particleRefreshTimer);
  }
  
  // Remove event listeners if needed
  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
  }
}

// Add cleanup on page unload
window.addEventListener('beforeunload', cleanup);

// Handle orientation change for mobile devices
window.addEventListener('orientationchange', function() {
  setTimeout(() => {
    // Reinitialize mobile optimizations after orientation change
    if (window.innerWidth <= 768) {
      initMobileOptimizations();
    }

    // Recalculate scroll position for toTop button
    if (domElements.toTop && window.scrollY > (window.innerWidth <= 768 ? 300 : 400)) {
      if (!domElements.toTop.classList.contains("show")) {
        domElements.toTop.style.display = "block";
        domElements.toTop.classList.add("show");
      }
    }
  }, 100);
});

// Development helper - remove in production
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.location.protocol === 'file:') {
  console.log('Development mode detected');
  console.log('Categories Data:', categoriesData);
  console.log('Products Data:', productsData);
  
  // Add development tools
  window.coffeeFactory = {
    showCategory,
    renderCategoryCards,
    showModal,
    closeModal,
    showNotification,
    createFloatingParticles,
    categoriesData,
    productsData,
    appState,
    domElements
  };
}

// Service Worker registration for PWA (optional)
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Final initialization and logging
console.log('Coffee Factory Premium Edition loaded successfully! 🚀☕');
console.log('Version: 5.0.0 - Performance Optimized Mobile Edition');
console.log('Features: Fixed Mobile Stuttering, Optimized INP, Enhanced Touch Support, Maintained Particles');

// Performance optimizations applied:
console.log('Optimizations Applied:');
console.log('- DOM caching for better element access');
console.log('- RequestAnimationFrame for smooth animations');
console.log('- Event delegation and passive listeners');
console.log('- Throttled scroll handlers');
console.log('- Reduced particles on mobile');
console.log('- GPU acceleration with transform3d');
console.log('- Optimized touch handlers with debouncing');
console.log('- Containment properties for layout optimization');