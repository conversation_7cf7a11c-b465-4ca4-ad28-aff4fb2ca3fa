// Categories Data with Icons
const categoriesData = [
  { 
    key: "Hot", 
    label: "Hot", 
    icon: `<img src="images/coffee.png" alt="Hot Coffee" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Ice", 
    label: "Ice", 
    icon: `<img src="images/cold-coffee.png" alt="Iced Coffee" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Frappe", 
    label: "Frappe", 
    icon: `<img src="images/frappe.png" alt="Frappe" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Refreshing", 
    label: "Refreshing", 
    icon: `<img src="images/refreshment.png" alt="Refreshing Drinks" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Juices", 
    label: "Juices", 
    icon: `<img src="images/juices.png" alt="Fresh Juices" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Milkshakes", 
    label: "Milkshakes", 
    icon: `<img src="images/chocolate.png" alt="Milkshakes" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Smoothies", 
    label: "Smoothies", 
    icon: `<img src="images/smoothie.png" alt="Smoothies" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Donuts", 
    label: "Donuts", 
    icon: `<img src="images/donut.png" alt="Donuts" style="width:56px;height:56px;margin-bottom:12px">`
  },
  { 
    key: "Cake", 
    label: "Cake", 
    icon: `<img src="images/cake.png" alt="Cakes" style="width:56px;height:56px;margin-bottom:12px">`
  }
];

// Products Data
const productsData = {
  Hot: [
    { name: "Espresso", price: "$2.50", img: "logo" },
    { name: "Latte", price: "$3.00", img: "logo" },
    { name: "Cappuccino", price: "$3.20", img: "logo" },
    { name: "Americano", price: "$2.80", img: "logo" },
    { name: "Macchiato", price: "$3.40", img: "logo" },
    { name: "Turkish Coffee", price: "$2.20", img: "logo" }
  ],
  Ice: [
    { name: "Iced Americano", price: "$3.20", img: "logo" },
    { name: "Cold Brew", price: "$3.50", img: "logo" },
    { name: "Iced Latte", price: "$3.80", img: "logo" },
    { name: "Iced Mocha", price: "$4.00", img: "logo" },
    { name: "Iced Cappuccino", price: "$3.60", img: "logo" },
    { name: "Nitro Coffee", price: "$4.20", img: "logo" }
  ],
  Frappe: [
    { name: "Caramel Frappe", price: "$3.80", img: "logo" },
    { name: "Mocha Frappe", price: "$4.00", img: "logo" },
    { name: "Vanilla Frappe", price: "$3.70", img: "logo" },
    { name: "Hazelnut Frappe", price: "$3.90", img: "logo" },
    { name: "Cookie Frappe", price: "$4.10", img: "logo" }
  ],
  Refreshing: [
    { name: "Lemon Mint", price: "$2.80", img: "logo" },
    { name: "Cucumber Water", price: "$2.50", img: "logo" },
    { name: "Green Tea", price: "$2.00", img: "logo" },
    { name: "Iced Tea", price: "$2.30", img: "logo" },
    { name: "Mint Lemonade", price: "$3.00", img: "logo" }
  ],
  Juices: [
    { name: "Orange Juice", price: "$2.50", img: "logo" },
    { name: "Apple Juice", price: "$2.30", img: "logo" },
    { name: "Mixed Berry", price: "$3.00", img: "logo" },
    { name: "Mango Juice", price: "$2.80", img: "logo" },
    { name: "Pineapple Juice", price: "$2.70", img: "logo" },
    { name: "Watermelon Juice", price: "$2.60", img: "logo" }
  ],
  Milkshakes: [
    { name: "Chocolate Shake", price: "$4.20", img: "logo" },
    { name: "Vanilla Shake", price: "$4.00", img: "logo" },
    { name: "Strawberry Shake", price: "$4.10", img: "logo" },
    { name: "Oreo Shake", price: "$4.50", img: "logo" },
    { name: "Banana Shake", price: "$3.90", img: "logo" }
  ],
  Smoothies: [
    { name: "Berry Smoothie", price: "$4.00", img: "logo" },
    { name: "Mango Smoothie", price: "$4.20", img: "logo" },
    { name: "Green Smoothie", price: "$4.50", img: "logo" },
    { name: "Tropical Smoothie", price: "$4.30", img: "logo" },
    { name: "Protein Smoothie", price: "$4.80", img: "logo" }
  ],
  Donuts: [
    { name: "Glazed Donut", price: "$1.50", img: "logo" },
    { name: "Chocolate Donut", price: "$1.80", img: "logo" },
    { name: "Jelly Donut", price: "$2.00", img: "logo" },
    { name: "Boston Cream", price: "$2.20", img: "logo" },
    { name: "Cinnamon Sugar", price: "$1.70", img: "logo" }
  ],
  Cake: [
    { name: "Chocolate Cake", price: "$4.50", img: "logo" },
    { name: "Cheesecake", price: "$5.00", img: "logo" },
    { name: "Red Velvet", price: "$4.80", img: "logo" },
    { name: "Carrot Cake", price: "$4.30", img: "logo" },
    { name: "Tiramisu", price: "$5.20", img: "logo" },
    { name: "Black Forest", price: "$4.90", img: "logo" }
  ]
};

// Global Variables
let modalTouchStart = 0;
let modalCurrentTranslate = 0;
let modalIsDragging = false;
let isScrolling = false;

// DOM Elements
const categoryList = document.getElementById("categoryList");
const categoryBar = document.getElementById("categoryBar");
const productsGrid = document.getElementById("products");
const topBar = document.getElementById("topBar");
const backBtn = document.getElementById("backBtn");
const logoImg = document.querySelector(".logo");

// Progress Bar Functions
function updateProgress(percent) {
  const fill = document.querySelector('.progress-fill');
  const text = document.querySelector('.progress-text');
  if (fill && text) {
    fill.style.width = `${percent}%`;
    text.textContent = `${Math.round(percent)}%`;
    
    if (percent < 30) {
      fill.style.background = '#ff5722';
    } else if (percent < 70) {
      fill.style.background = '#ff9800';
    } else {
      fill.style.background = 'linear-gradient(90deg, #DAA520, #FFD700)';
      fill.style.backgroundSize = '200% 100%';
      fill.style.animation = 'shimmer 2s infinite';
    }
  }
}

function animateProgress(duration = 3000) {
  let start = null;
  const max = 100;
  
  function step(timestamp) {
    if (!start) start = timestamp;
    const progress = Math.min((timestamp - start) / duration * max, max);
    updateProgress(progress);
    if (progress < max) {
      window.requestAnimationFrame(step);
    } else {
      setTimeout(() => {
        document.getElementById('demoProgress').style.opacity = '0';
        setTimeout(() => {
          document.getElementById('demoProgress').style.display = 'none';
        }, 500);
      }, 500);
    }
  }
  window.requestAnimationFrame(step);
}

// Intro Animation
window.addEventListener("load", () => {
  try {
    animateProgress();
    
    setTimeout(() => {
      const intro = document.getElementById("intro");
      const main = document.getElementById("main");
      const progressBar = document.getElementById('demoProgress');

      if (!intro || !main || !progressBar) {
        console.error('Essential elements not found');
        return;
      }

      intro.style.opacity = 0;

      setTimeout(() => {
        intro.style.display = "none";
        progressBar.style.display = "none";
        main.style.display = "block";
        document.body.style.overflow = "auto";
        
        try {
          initSlideshow();
          renderCategoryCards();
          createFloatingParticles();
        } catch (e) {
          console.error('Initialization error:', e);
        }
      }, 1000);
    }, 3500);
  } catch (e) {
    console.error('Load error:', e);
    // Fallback - show main content if other operations fail
    const main = document.getElementById("main");
    if (main) {
      main.style.display = "block";
      document.body.style.overflow = "auto";
    }
    const progressBar = document.getElementById('demoProgress');
    if (progressBar) progressBar.style.display = "none";
  }
});

// Category Cards Rendering
function renderCategoryCards() {
  categoryList.classList.remove("fade-in", "fade-out");
  categoryList.innerHTML = "";
  categoryList.style.display = "grid";
  categoryList.style.opacity = "1";
  
  if (topBar) topBar.style.display = "none";
  if (productsGrid) productsGrid.style.display = "none";
  
  categoriesData.forEach((cat, index) => {
    const div = document.createElement("div");
    div.className = `category-card cat-${cat.key.toLowerCase()}`;
    div.innerHTML = `
      <span class="cat-icon">${cat.icon}</span>
      <h3 class="cat-label">${cat.label}</h3>
    `;
    
    const delay = (Math.floor(index / 2) * 0.1) + ((index % 2) * 0.05);
    div.style.animationDelay = `${delay}s`;
    
    div.onclick = () => showCategory(cat.key);
    div.addEventListener('touchstart', () => {
      div.style.transform = 'scale(0.95)';
    });
    div.addEventListener('touchend', () => {
      div.style.transform = '';
    });
    
    categoryList.appendChild(div);
  });
}

// Show Category Products
function showCategory(cat) {
  if (logoImg) logoImg.style.display = "none";
  const gallery = document.getElementById("water-gallery");
  if (gallery) gallery.style.display = "none";
  
  Array.from(categoryList.children).forEach(el => el.classList.remove("active"));
  const idx = categoriesData.findIndex(c => c.key === cat);
  if (idx !== -1 && categoryList.children[idx]) {
    categoryList.children[idx].classList.add("active");
  }

  fadeHide(categoryList, () => {
    topBar.style.display = "block";
    renderCategoryBar(cat);

    productsGrid.innerHTML = "";
    const items = productsData[cat];
    if (!items || !items.length) {
      productsGrid.innerHTML = "<p style='text-align:center;color:#ccc;grid-column:1/-1;padding:2rem;'>No products in this category yet.</p>";
    } else {
      items.forEach((item, i) => {
        const card = document.createElement("div");
        card.className = "product-card";
        card.style.animationDelay = `${i * 0.1}s`;
        card.innerHTML = `
          <img src="images/logo.png" alt="${item.name}" style="width: 120px; height: 120px; object-fit: contain; margin-bottom: 15px; filter: drop-shadow(0 0 10px rgba(218, 165, 32, 0.3)); transition: transform 0.3s ease;">
          <h3>${item.name}</h3>
          <p>${item.price}</p>
        `;
        
        card.onclick = function(e) {
          e.stopPropagation();
          
          // Remove expanded state from all other cards
          document.querySelectorAll('.product-card.expanded').forEach(otherCard => {
            if (otherCard !== card) {
              otherCard.classList.remove('expanded');
            }
          });
          
          // Toggle expanded state for clicked card
          card.classList.toggle('expanded');
          
          // Create ripple effect
          createRippleEffect(card, e);
        };
        
        productsGrid.appendChild(card);
      });
    }
    fadeShow(productsGrid);
  });
}

// Category Bar Rendering
function renderCategoryBar(activeCat) {
  if (!categoryBar) return;
  
  categoryBar.innerHTML = "";
  categoriesData.forEach((cat) => {
    if (cat.key === activeCat) return;
    const btn = document.createElement("button");
    btn.textContent = cat.label;
    btn.onclick = () => showCategory(cat.key);
    categoryBar.appendChild(btn);
  });
}

// Back Button
if (backBtn) {
  backBtn.onclick = () => {
    if (logoImg) logoImg.style.display = "block";
    const gallery = document.getElementById("water-gallery");
    if (gallery) gallery.style.display = "block";
    
    fadeHide(productsGrid, () => {
      topBar.style.display = "none";
      renderCategoryCards();
      fadeShow(categoryList);
    });
  };
}

// Slideshow
function initSlideshow() {
  const slides = document.querySelectorAll('.slide');
  if (!slides.length) return;
  
  let currentSlide = 0;
  
  function showSlide(n) {
    slides.forEach(slide => slide.classList.remove('fade'));
    if (slides[n]) {
      slides[n].classList.add('fade');
    }
  }
  
  function nextSlide() {
    currentSlide = (currentSlide + 1) % slides.length;
    showSlide(currentSlide);
  }
  
  showSlide(0);
  setInterval(nextSlide, 5000);
}

// Fade Functions
const ANIM_DURATION = 150;
function fadeShow(el, duration = ANIM_DURATION) {
  if (!el) return;

  el.style.display = '';          
  el.classList.remove('fade-out');
  el.classList.add('fade-in');

  setTimeout(() => el.classList.remove('fade-in'), duration);
}

function fadeHide(el, cb, duration = ANIM_DURATION) {
  if (!el) return;

  el.classList.remove('fade-in');
  el.classList.add('fade-out');

  if (typeof cb === 'function') cb();

  setTimeout(() => {
    el.style.display = 'none';
    el.classList.remove('fade-out');
  }, duration);
}

// Enhanced Modal System with Touch Support
function initModalTouchSupport() {
  const modals = document.querySelectorAll('.modal-glass');
  
  modals.forEach(modal => {
    const modalContent = modal.querySelector('.modal-content');
    const modalHandle = modal.querySelector('.modal-handle');
    
    if (!modalHandle || !modalContent) return;
    
    let startY = 0;
    let currentY = 0;
    let isDragging = false;
    
    // Touch events for modal dragging
    modalHandle.addEventListener('touchstart', handleTouchStart, { passive: false });
    modalContent.addEventListener('touchmove', handleTouchMove, { passive: false });
    modalContent.addEventListener('touchend', handleTouchEnd, { passive: false });
    
    // Mouse events for desktop
    modalHandle.addEventListener('mousedown', handleMouseDown);
    
    function handleTouchStart(e) {
      startY = e.touches[0].clientY;
      isDragging = true;
      modalContent.style.transition = 'none';
      modalContent.style.userSelect = 'none';
    }
    
    function handleTouchMove(e) {
      if (!isDragging) return;
      
      currentY = e.touches[0].clientY;
      const deltaY = currentY - startY;
      
      if (deltaY > 0) { // Only allow dragging down
        e.preventDefault();
        modalContent.style.transform = `translateY(${deltaY}px)`;
        
        // Add opacity effect based on drag distance
        const opacity = Math.max(0.3, 1 - (deltaY / 400));
        modal.style.background = `rgba(0,0,0,${0.8 * opacity})`;
      }
    }
    
    function handleTouchEnd(e) {
      if (!isDragging) return;
      
      isDragging = false;
      modalContent.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
      modalContent.style.userSelect = '';
      
      const deltaY = currentY - startY;
      
      // If dragged more than 120px, close the modal
      if (deltaY > 120) {
        closeModal(modal);
      } else {
        // Snap back to original position
        modalContent.style.transform = 'translateY(0)';
        modal.style.background = 'rgba(0,0,0,0.8)';
      }
      
      startY = 0;
      currentY = 0;
    }
    
    function handleMouseDown(e) {
      startY = e.clientY;
      isDragging = true;
      modalContent.style.transition = 'none';
      e.preventDefault();
      
      function handleMouseMove(e) {
        if (!isDragging) return;
        
        currentY = e.clientY;
        const deltaY = currentY - startY;
        
        if (deltaY > 0) {
          modalContent.style.transform = `translateY(${deltaY}px)`;
          const opacity = Math.max(0.3, 1 - (deltaY / 400));
          modal.style.background = `rgba(0,0,0,${0.8 * opacity})`;
        }
      }
      
      function handleMouseUp(e) {
        if (!isDragging) return;
        
        isDragging = false;
        modalContent.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
        
        const deltaY = currentY - startY;
        
        if (deltaY > 120) {
          closeModal(modal);
        } else {
          modalContent.style.transform = 'translateY(0)';
          modal.style.background = 'rgba(0,0,0,0.8)';
        }
        
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        
        startY = 0;
        currentY = 0;
      }
      
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  });
}

// Enhanced Modal Functions
function showModal(modal) {
  if (!modal) return;
  
  modal.classList.add('show');
  document.body.style.overflow = 'hidden';
  
  // Reset modal position
  const modalContent = modal.querySelector('.modal-content');
  if (modalContent) {
    modalContent.style.transform = 'translateY(0)';
    modalContent.style.animation = 'modalSlideUp 0.5s cubic-bezier(.2,.8,.2,1) both';
  }
  modal.style.background = 'rgba(0,0,0,0.8)';
}

function closeModal(modal) {
  if (!modal) return;
  
  const modalContent = modal.querySelector('.modal-content');
  
  if (modalContent) {
    modalContent.style.animation = 'modalSlideDown 0.3s ease-out both';
  }
  
  setTimeout(() => {
    modal.classList.remove('show');
    document.body.style.overflow = 'auto';
    if (modalContent) {
      modalContent.style.animation = '';
      modalContent.style.transform = '';
    }
  }, 300);
}

// Enhanced Notification System
function showNotification(message, type = 'info') {
  // Remove any existing notifications
  const existingNotifications = document.querySelectorAll('.notification');
  existingNotifications.forEach(notif => {
    if (document.body.contains(notif)) {
      document.body.removeChild(notif);
    }
  });
  
  const notification = document.createElement('div');
  notification.className = 'notification';
  notification.style.cssText = `
    position: fixed;
    top: 80px;
    right: 15px;
    left: 15px;
    max-width: 350px;
    margin: 0 auto;
    background: ${type === 'error' ? 
      'linear-gradient(145deg, #ff4444, #cc0000)' : 
      'linear-gradient(145deg, #DAA520, #B8860B)'};
    color: white;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
    z-index: 3000;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    word-wrap: break-word;
    animation: slideInDown 0.4s cubic-bezier(.2,.8,.2,1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    cursor: pointer;
    user-select: none;
  `;
  
  // Add icon based on type
  const icon = type === 'error' ? '⚠️' : '✅';
  notification.innerHTML = `<span style="margin-right: 8px;">${icon}</span>${message}`;
  
  document.body.appendChild(notification);
  
  // Auto remove after 4 seconds
  const autoRemove = setTimeout(() => {
    removeNotification(notification);
  }, 4000);
  
  // Add click to dismiss
  notification.addEventListener('click', () => {
    clearTimeout(autoRemove);
    removeNotification(notification);
  });
  
  function removeNotification(notif) {
    if (!document.body.contains(notif)) return;
    
    notif.style.animation = 'slideOutUp 0.3s ease-out forwards';
    setTimeout(() => {
      if (document.body.contains(notif)) {
        document.body.removeChild(notif);
      }
    }, 300);
  }
}

// Footer Button Animation
function animateFooterButton(clickedId) {
  const buttons = ['mainMenuBtn', 'rateUsBtn', 'aboutUsBtn'];
  buttons.forEach(id => {
    const btn = document.getElementById(id);
    if (!btn) return;
    
    if (id === clickedId) {
      btn.style.transform = 'translateY(-8px) scale(1.05)';
      btn.style.transition = 'transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
      setTimeout(() => {
        btn.style.transform = 'translateY(0) scale(1)';
      }, 300);
    } else {
      btn.style.transform = 'none';
    }
  });
}

// Enhanced Visual Effects
function createFloatingParticles() {
  const particleCount = 25;
  const existingParticles = document.querySelectorAll('.floating-particle');
  existingParticles.forEach(p => p.remove());
  
  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    particle.className = 'floating-particle';
    particle.style.cssText = `
      position: fixed;
      width: ${Math.random() * 4 + 2}px;
      height: ${Math.random() * 4 + 2}px;
      background: rgba(218, 165, 32, ${Math.random() * 0.5 + 0.2});
      border-radius: 50%;
      pointer-events: none;
      z-index: 1;
      left: ${Math.random() * 100}vw;
      top: ${Math.random() * 100}vh;
      animation: float ${Math.random() * 10 + 15}s linear infinite;
    `;
    document.body.appendChild(particle);
  }
}

// Enhanced Product Card Interactions
function enhanceProductCards() {
  document.addEventListener('click', (e) => {
    if (e.target.closest('.product-card')) {
      return; // Let the card handle its own click
    } else {
      // Remove expanded state when clicking outside
      document.querySelectorAll('.product-card.expanded').forEach(card => {
        card.classList.remove('expanded');
      });
    }
  });
}

function createRippleEffect(element, event) {
  // Remove existing ripples
  element.querySelectorAll('.ripple').forEach(r => r.remove());
  
  const ripple = document.createElement('div');
  ripple.className = 'ripple';
  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = event.clientX - rect.left - size / 2;
  const y = event.clientY - rect.top - size / 2;
  
  ripple.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    left: ${x}px;
    top: ${y}px;
    background: rgba(218, 165, 32, 0.3);
    border-radius: 50%;
    pointer-events: none;
    transform: scale(0);
    animation: ripple 0.6s ease-out;
    z-index: 1;
  `;
  
  element.style.position = 'relative';
  element.appendChild(ripple);
  
  setTimeout(() => {
    if (ripple.parentNode) {
      ripple.remove();
    }
  }, 600);
}

// Fixed Scroll to Top Functionality
const toTop = document.getElementById("toTop");

// Improved scroll handler - fixed the performance issue
let scrollTimeout = null;
let isScrollHandlerRunning = false;

function handleScroll() {
  if (isScrollHandlerRunning) return;
  isScrollHandlerRunning = true;
  
  requestAnimationFrame(() => {
    const scrolled = window.scrollY;
    
    // Handle scroll to top button
    if (toTop) {
      if (scrolled > 400) {
        toTop.style.display = "block";
        toTop.classList.add("show");
      } else {
        toTop.classList.remove("show");
        setTimeout(() => {
          if (!toTop.classList.contains("show")) {
            toTop.style.display = "none";
          }
        }, 300);
      }
    }
    
    // Optional: Light parallax effect (removed heavy operations)
    const gallery = document.getElementById('water-gallery');
    if (gallery && gallery.style.display !== 'none' && scrolled < 1000) {
      const rate = scrolled * -0.05; // Reduced intensity
      gallery.style.transform = `translateY(${rate}px)`;
    }
    
    isScrollHandlerRunning = false;
  });
}

// Throttled scroll event listener
function throttledScrollHandler() {
  if (scrollTimeout) return;
  
  scrollTimeout = setTimeout(() => {
    handleScroll();
    scrollTimeout = null;
  }, 16); // ~60fps
}

// Add scroll event listener
window.addEventListener('scroll', throttledScrollHandler, { passive: true });

// Scroll to top function
function scrollToTop() {
  window.scrollTo({ 
    top: 0, 
    behavior: "smooth" 
  });
}

if (toTop) {
  toTop.onclick = scrollToTop;
}

// Add scroll to top for category buttons and cards
function initScrollToTopListeners() {
  // Scroll to top when clicking category buttons in category bar
  const categoryBar = document.getElementById("categoryBar");
  if (categoryBar) {
    categoryBar.addEventListener("click", function(e) {
      if (e.target.tagName === "BUTTON") {
        scrollToTop();
      }
    });
  }

  // Scroll to top when clicking back button
  const backBtn = document.getElementById("backBtn");
  if (backBtn) {
    backBtn.addEventListener("click", scrollToTop);
  }
}

// Initialize scroll to top listeners when DOM is loaded
window.addEventListener('DOMContentLoaded', function() {
  initScrollToTopListeners();
});

// Main DOMContentLoaded Event
window.addEventListener('DOMContentLoaded', function() {
  // Initialize modal touch support
  initModalTouchSupport();
  
  // Initialize enhanced interactions
  enhanceProductCards();
  
  // Main Menu Button
  const mainMenuBtn = document.getElementById("mainMenuBtn");
  if (mainMenuBtn) {
    mainMenuBtn.addEventListener("click", () => {
      animateFooterButton('mainMenuBtn');
      const mainSection = document.getElementById("main");
      if (mainSection) {
        mainSection.style.display = "block";
      }
      const gallery = document.getElementById("water-gallery");
      if (gallery) gallery.style.display = "block";
      if (logoImg) logoImg.style.display = "block";
      renderCategoryCards();
      
      // Close any open modals
      document.querySelectorAll('.modal-glass.show').forEach(modal => {
        closeModal(modal);
      });
    });
  }

  // Rate Us Modal
  const rateUsBtn = document.getElementById("rateUsBtn");
  const rateModal = document.getElementById("rateModal");
  if (rateUsBtn && rateModal) {
    rateUsBtn.onclick = () => {
      animateFooterButton('rateUsBtn');
      showModal(rateModal);
    };
  }
  
  const cancelRate = document.getElementById("cancelRate");
  const cancelRate2 = document.getElementById("cancelRate2");
  if (cancelRate) {
    cancelRate.onclick = () => closeModal(rateModal);
  }
  if (cancelRate2) {
    cancelRate2.onclick = () => closeModal(rateModal);
  }

  // About Us Modal
  const aboutUsBtn = document.getElementById("aboutUsBtn");
  const aboutModal = document.getElementById("aboutModal");
  if (aboutUsBtn && aboutModal) {
    aboutUsBtn.onclick = () => {
      animateFooterButton('aboutUsBtn');
      showModal(aboutModal);
    };
  }
  
  const closeAbout = document.getElementById("closeAbout");
  if (closeAbout) {
    closeAbout.onclick = () => closeModal(aboutModal);
  }

  // Enhanced Rating System
  const starContainers = document.querySelectorAll("#rateModal .stars");
  const ratings = {};
  
  starContainers.forEach(container => {
    const question = container.dataset.question;
    ratings[question] = 0;
    container.innerHTML = "";
    
    for (let i = 1; i <= 5; i++) {
      const star = document.createElement("span");
      star.textContent = "★";
      star.dataset.rating = i;
      star.style.cssText = `
        cursor: pointer;
        font-size: 24px;
        color: #444;
        transition: all 0.2s ease;
        user-select: none;
        -webkit-user-select: none;
        -webkit-tap-highlight-color: transparent;
      `;
      
      // Touch events for mobile
      star.addEventListener("touchstart", (e) => {
        e.preventDefault();
        highlightStars(container, i);
      });
      
      star.addEventListener("touchend", (e) => {
        e.preventDefault();
        ratings[question] = i;
        highlightStars(container, i);
        
        // Haptic feedback if available
        if (window.navigator && window.navigator.vibrate) {
          window.navigator.vibrate(50);
        }
      });
      
      // Mouse events for desktop
      star.addEventListener("mouseenter", () => {
        highlightStars(container, i);
      });
      
      star.addEventListener("mouseleave", () => {
        highlightStars(container, ratings[question]);
      });
      
      star.addEventListener("click", (e) => {
        e.preventDefault();
        ratings[question] = i;
        highlightStars(container, i);
      });
      
      container.appendChild(star);
    }
  });

  function highlightStars(container, count) {
    Array.from(container.children).forEach((star, index) => {
      if (index < count) {
        star.style.color = "#DAA520";
        star.style.transform = "scale(1.2)";
        star.style.textShadow = "0 0 15px rgba(218, 165, 32, 0.7)";
      } else {
        star.style.color = "#444";
        star.style.transform = "scale(1)";
        star.style.textShadow = "none";
      }
    });
  }

  // Enhanced form submission with better validation
  const sendRateBtn = document.getElementById("sendRate");
  if (sendRateBtn) {
    sendRateBtn.addEventListener("click", () => {
      const name = document.getElementById("rateName")?.value.trim() || "";
      const email = document.getElementById("rateEmail")?.value.trim() || "";
      const feedback = document.getElementById("rateOpinion")?.value.trim() || "";
      
      // Enhanced validation
      if (!name) {
        showNotification("Please enter your name", "error");
        document.getElementById("rateName")?.focus();
        return;
      }
      
      if (name.length < 2) {
        showNotification("Name must be at least 2 characters", "error");
        document.getElementById("rateName")?.focus();
        return;
      }
      
      if (!email) {
        showNotification("Please enter your email", "error");
        document.getElementById("rateEmail")?.focus();
        return;
      }
      
      // Email validation
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(email)) {
        showNotification("Please enter a valid email address", "error");
        document.getElementById("rateEmail")?.focus();
        return;
      }
      
      // Check if at least one rating is given
      const hasRating = Object.values(ratings).some(rating => rating > 0);
      if (!hasRating) {
        showNotification("Please give at least one rating", "error");
        return;
      }
      
      // Calculate average rating
      const totalRatings = Object.values(ratings).filter(r => r > 0);
      const averageRating = totalRatings.length > 0 ? 
        (totalRatings.reduce((sum, rating) => sum + rating, 0) / totalRatings.length).toFixed(1) : 0;
      
      // Collect all ratings
      const ratingData = {
        name,
        email,
        feedback,
        ratings,
        averageRating,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        language: navigator.language
      };
      
      console.log("Rating submitted:", ratingData);
      
      // Show success message with rating summary
      showNotification(`Thank you ${name}! Your rating (${averageRating}★) has been submitted successfully! 🎉`, "success");
      
      // Add loading state to button
      const originalText = sendRateBtn.textContent;
      sendRateBtn.textContent = "Submitting...";
      sendRateBtn.disabled = true;
      sendRateBtn.style.opacity = "0.6";
      
      // Simulate sending (in real app, this would be an API call)
      setTimeout(() => {
        sendRateBtn.textContent = originalText;
        sendRateBtn.disabled = false;
        sendRateBtn.style.opacity = "1";
        closeModal(rateModal);
        
        // Clear form
        const nameInput = document.getElementById("rateName");
        const emailInput = document.getElementById("rateEmail");
        const feedbackInput = document.getElementById("rateOpinion");
        
        if (nameInput) nameInput.value = "";
        if (emailInput) emailInput.value = "";
        if (feedbackInput) feedbackInput.value = "";
        
        Object.keys(ratings).forEach(key => ratings[key] = 0);
        starContainers.forEach(container => highlightStars(container, 0));
      }, 1500);
    });
  }

  // Enhanced modal closing - close when clicking outside or pressing escape
  document.querySelectorAll('.modal-glass').forEach(modal => {
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal(modal);
      }
    });
  });
  
  // Escape key to close modals
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      const openModal = document.querySelector('.modal-glass.show');
      if (openModal) {
        closeModal(openModal);
      }
    }
  });
});

// Add dynamic CSS animations
const dynamicStyles = `
@keyframes slideInDown {
  from {
    transform: translateY(-100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100px);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes modalSlideUp {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalSlideDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(100%);
  }
}

/* Smooth transitions for all interactive elements */
button, .category-card, .product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects */
.category-card:hover {
  transform: scale(1.05) translateY(-5px);
  box-shadow: 0 10px 25px rgba(218, 165, 32, 0.3);
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* Improved scroll to top button */
#toTop {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: linear-gradient(145deg, #DAA520, #B8860B);
  color: #fff;
  border: none;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(218, 165, 32, 0.3);
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 1000;
}

#toTop.show {
  opacity: 1;
  transform: scale(1);
}

#toTop:hover {
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 6px 20px rgba(218, 165, 32, 0.4);
}

#toTop:active {
  transform: scale(0.95);
}

/* Loading state styles */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #DAA520;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced focus states for accessibility */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid #DAA520;
  outline-offset: 2px;
}

/* Improved touch targets */
@media (max-width: 768px) {
  button,
  .category-card,
  .product-card {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Fade animations for content switching */
.fade-in {
  animation: fadeSlide 0.5s ease both;
}

.fade-out {
  animation: fadeOut 0.3s ease both;
}

@keyframes fadeSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}
`;

// Add dynamic styles to head
const dynamicStyleSheet = document.createElement('style');
dynamicStyleSheet.textContent = dynamicStyles;
document.head.appendChild(dynamicStyleSheet);

// Error handling and debugging
window.addEventListener('error', (e) => {
  console.error('JavaScript Error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled Promise Rejection:', e.reason);
});

// Performance monitoring
if ('performance' in window) {
  window.addEventListener('load', () => {
    setTimeout(() => {
      const perfData = performance.getEntriesByType('navigation')[0];
      console.log('Page Load Performance:', {
        loadTime: perfData.loadEventEnd - perfData.loadEventStart,
        domReady: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
        totalTime: perfData.loadEventEnd - perfData.fetchStart
      });
    }, 1000);
  });
}

// Development helper - remove in production
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.location.protocol === 'file:') {
  console.log('Development mode detected');
  console.log('Categories Data:', categoriesData);
  console.log('Products Data:', productsData);
  
  // Add development tools
  window.coffeeFactory = {
    showCategory,
    renderCategoryCards,
    showModal,
    closeModal,
    showNotification,
    createFloatingParticles,
    categoriesData,
    productsData
  };
}

// Initialize particles refresh timer
setInterval(() => {
  const particles = document.querySelectorAll('.floating-particle');
  if (particles.length < 15) {
    createFloatingParticles();
  }
}, 30000);

// Final initialization
console.log('Coffee Factory Premium Edition loaded successfully! 🚀☕');
console.log('Version: 4.1.0 - Fixed Scroll Performance Edition');
console.log('Features: Fixed Scroll Issues, Touch Support, Responsive Design, Enhanced UX');

// Service Worker registration for PWA (optional)
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}